<template>
  <view class="flex flex-col items-center justify-center" style="height: 100vh;">
    <view class="fixed top-0 left-0 w-full h-10 header flex items-center p-2 bg-[#FEFCF8]" style="z-index: 10;"
      v-if="false">
      <up-icon class="absolute left-0" size="24" color="#666" name="arrow-left" @click="goBack"></up-icon>
      <text class="text-center flex-1 text-lg font-medium">美学设计</text>
    </view>

    <up-navbar title="美学设计" @leftClick="goBack">
    </up-navbar>

    <aestheticDiagnosis :reports="reports" :icons="icons" :buttonIndex="buttonIndex" v-if="buttonIndex == 0"
      :compareFlag="compareFlag" :peopleImg='peopleImg' v-model:activeReport="activeReport"
      @update:loading="updateLoading" @update:percent="updatePercent" />
    <view style="transition: all 0.3s;" class="w-full flex-1 flex flex-col items-center mt-24" v-if="buttonIndex == -1">

      <img ref="beforeimgRef" :src="peopleImg" class="w-full object-cover" mode="aspectFill">
      <!-- <ImgCompare v-else :before="peopleImg" :after="peopleImg" :height="beforeImgHeight" /> -->


    </view>
  </view>
  <!-- 底部导航栏1 -->
  <view v-show="!activeReport"
    class="fixed bottom-0 left-0 w-full bg-white flex flex-row justify-between items-center px-6 py-4 border-t border-[#f0f0f0] iconGround transition-transform duration-300 transform"
    :class="{ 'translate-y-0': !activeReport, 'translate-y-full': activeReport }">
    <!-- 美学诊断 -->
    <view v-for="(item, index) in icons" class="flex flex-col items-center flex-1" @click="selectIcon(index)">
      <img :src="item.selected ? item.activeUrl : item.url" alt="">
      <text class="text-[#6d6d6d] text-xs mt-1">{{ item.label }}</text>
    </view>
  </view>



  <!-- Loading Overlay -->
  <view v-if="loading && buttonIndex != -1" class="loading-overlay">
    <view class="loading-content">
      <text class="loading-text" v-if="s == '正在生成中'">分析中,{{ s }}...</text>
      <text class="loading-text" v-else>分析中,预计还有{{ s }}秒...</text>
      <progress class="loading-progress" activeColor="#1890ff" backgroundColor="#f0f0f0" :percent="percent" />
    </view>
  </view>



</template>

<script setup>
import aestheticDiagnosis from "@/components/aestheticDiagnosis/aestheticDiagnosis.vue";
import { ref, computed, watch } from 'vue';


import {
  onLoad,
  onShow
} from '@dcloudio/uni-app';


const peopleImg = ref('/static/imgs/people.png')
const beforeimgRef = ref(null)

onLoad((options) => {
  if (options.img) {
    peopleImg.value = options.img
  }
})


let s = ref('60')
setInterval(() => {
  if(s.value <= 0 || s.value == '正在生成中'){
    return s.value = '正在生成中'
  }
  let sc = parseInt(s.value);
  sc = sc - 1
  if(sc <= 0) {
    s.value = '正在生成中'
  } else {
    s.value = sc.toString();
  }
}, 1000);
const reports = ['诊断报告', '美学方案', '专属推荐'];
// 定义图标状态
const icons = ref([
  { label: '美学诊断', url: '/static/icons/skin_icon1.png', activeUrl: '/static/icons/skin_icon1_active.png', selected: true, reports },
  { label: '面部分析', url: '/static/icons/skin_icon2.png', activeUrl: '/static/icons/skin_icon2_active.png', selected: false },
  { label: '皮肤分析', url: '/static/icons/skin_icon3.png', activeUrl: '/static/icons/skin_icon3_active.png', selected: false },
  // { label: '3D仿真', url: '/static/icons/skin_icon4.png', activeUrl: '/static/icons/skin_icon4_active.png', selected: false }
])






const activeReport = ref('')// 选择图标方法
let buttonIndex = ref(0)
let percent = ref(0);
const loading = ref(true)
const selectIcon = (index) => {
  buttonIndex.value = index
  // 重置所有图标状态
  icons.value.forEach((icon, i) => {
    icon.selected = i === index
  })
}

// 更新loading状态
const updateLoading = (value) => {
  loading.value = value
}

// 更新进度
const updatePercent = (value) => {
  percent.value = value
}



// 关闭弹窗
const closePopup = () => {
  // popupVisible.value = false;
  setTimeout(() => {
    activeReport.value = '';
    buttonIndex.value = -1
    icons.value.forEach((icon) => {
      icon.selected = false;
    })
  }, 300);
}


// 返回上一页
const goBack = () => {
  if (activeReport.value) {
    closePopup()
  } else {
    uni.navigateBack()
  }

};


const compareFlag = computed(() => {
  return previewFlag.value && activeReport.value == '美学方案'
})

const previewFlag = ref(false)
watch(() => activeReport.value, (newVal) => {
  console.log('父组件:', newVal);
});

</script>

<style scoped lang="scss">
.iconGround {
  img {
    width: 50rpx;
    height: 50rpx;
    margin-bottom: 10rpx;
  }
}

/* Loading Overlay Styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9;
}

.loading-content {
  // background-color: white;
  border-radius: 10rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
}

.loading-progress {
  width: 100%;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #fff;
}

/* 全局样式 */
page {
  background-color: #FEFCF8;
  font-family: 'Noto Sans SC', sans-serif;
}
</style>